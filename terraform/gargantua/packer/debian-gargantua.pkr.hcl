packer {
  required_plugins {
    proxmox = {
      version = ">= 1.1.8"
      source  = "github.com/hashicorp/proxmox"
    }
  }
}

# 变量定义
variable "proxmox_api_url" {
  type        = string
  description = "Proxmox API URL"
  default     = "https://**************:8006/api2/json"
}

variable "proxmox_username" {
  type        = string
  description = "Proxmox username"
  default     = "root@pam"
}

variable "proxmox_password" {
  type        = string
  description = "Proxmox password"
  sensitive   = true
}

variable "proxmox_node" {
  type        = string
  description = "Proxmox node name"
  default     = "batata"
}

variable "template_name" {
  type        = string
  description = "Template name"
  default     = "debian-12-gargantua"
}

# Debian 12 LXC 模板构建
source "proxmox-lxc" "debian-gargantua" {
  # Proxmox 连接配置
  proxmox_url              = var.proxmox_api_url
  username                 = var.proxmox_username
  password                 = var.proxmox_password
  insecure_skip_tls_verify = true

  # 节点和模板配置
  node                 = var.proxmox_node
  template_name        = var.template_name
  template_description = "Debian 12 optimized for Gargantua router containers"

  # LXC 容器配置
  os_template          = "local:vztmpl/debian-12-standard_12.2-1_amd64.tar.zst"
  unprivileged         = true

  # 资源配置
  cores                = 1
  memory               = 512
  swap                 = 512

  # 存储配置
  disk_size            = "8G"
  storage_pool         = "local-lvm"

  # 网络配置
  network_adapters {
    name     = "eth0"
    bridge   = "vmbr0"
    firewall = false
  }

  # SSH 配置
  ssh_username = "root"
  ssh_password = "packer"
  ssh_timeout = "15m"
}

# 构建配置
build {
  sources = ["source.proxmox-lxc.debian-gargantua"]
  
  # 等待容器启动
  provisioner "shell" {
    inline = [
      "sleep 30",
      "echo 'Container is ready for configuration'"
    ]
  }

  # 设置 root 密码
  provisioner "shell" {
    inline = [
      "echo 'root:packer' | chpasswd"
    ]
  }

  # 更新系统
  provisioner "shell" {
    inline = [
      "apt-get update",
      "apt-get upgrade -y"
    ]
  }

  # 安装必要的软件包
  provisioner "shell" {
    inline = [
      "DEBIAN_FRONTEND=noninteractive apt-get install -y curl wget gnupg2 software-properties-common",
      "DEBIAN_FRONTEND=noninteractive apt-get install -y iptables iproute2 iputils-ping net-tools",
      "DEBIAN_FRONTEND=noninteractive apt-get install -y systemd-resolved openssh-server",
      "DEBIAN_FRONTEND=noninteractive apt-get install -y python3 python3-pip",
      "DEBIAN_FRONTEND=noninteractive apt-get install -y htop vim nano iptables-persistent"
    ]
  }

  # 启用 IP 转发
  provisioner "shell" {
    inline = [
      "echo 'net.ipv4.ip_forward=1' >> /etc/sysctl.conf",
      "echo 'net.ipv6.conf.all.forwarding=1' >> /etc/sysctl.conf"
    ]
  }

  # 配置 SSH
  provisioner "shell" {
    inline = [
      "systemctl enable ssh",
      "sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config",
      "sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/' /etc/ssh/sshd_config",
      "mkdir -p /root/.ssh",
      "chmod 700 /root/.ssh"
    ]
  }

  # 清理系统
  provisioner "shell" {
    inline = [
      "apt-get autoremove -y",
      "apt-get autoclean",
      "rm -rf /var/lib/apt/lists/*",
      "rm -rf /tmp/*",
      "rm -rf /var/tmp/*",
      "history -c"
    ]
  }
}
