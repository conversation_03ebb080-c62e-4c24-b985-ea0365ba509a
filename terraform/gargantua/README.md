# Gargantua 软路由

基于 LXC 容器的跨地域软路由，通过 Headscale 实现网络互联。

## 网络架构

```
qddt (*************/24) ←→ Headscale ←→ unsh (************/24)
                                ↕
                        sh (************/24)
```

- **interstellar** (unsh): ************/24
- **cooper** (sh): ************/24

## 快速部署

1. **配置**
```bash
cp terraform.tfvars.example terraform.tfvars
vim terraform.tfvars
```

2. **部署**
```bash
make gargantua-full-deploy
```

3. **验证**
```bash
ansible gargantua -i ansible/inventory/hosts.yml -m ping
```

## 文件结构

- `providers.tf` - Proxmox 连接配置
- `interstellar.tf` - unsh 容器配置
- `cooper.tf` - sh 容器配置
- `variables.tf` - 变量定义
- `outputs.tf` - 输出信息
