# Ansible connection settings
ansible_python_interpreter: /usr/bin/python3

# Software versions
containerd_version: 2.1.1
nerdctl_version: 2.1.2
runc_version: 1.3.0
cni_plugins_version: 1.7.1
headscale_version: 0.26.1

# Traefik configuration
traefik_domain: noahgao.net
traefik_acme_email: <EMAIL>

# Registry mirrors configuration
registry_mirrors:
  - docker.io
  - ghcr.io
registry_mirror_proxy: "https://docker.gh-proxy.com"

# Headscale configuration
headscale_domain: "headscale.{{ traefik_domain }}"
headscale_url: "https://{{ headscale_domain }}"
