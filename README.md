# Homelab 分布式架构

这是一个基于 Ansible / Terraform / Makefile 的分布式 Homelab 架构部署仓库。该架构横跨多个地区，实现了高可用、分布式计算和存储能力。

## 架构总览

### 节点分布

| 地区 | 节点名称 | 类型 | 配置 | 用途 |
|------|----------|------|------|------|
| 青岛 | fork | 阿里云 ECS | 2C4G, 50G 云硬盘 | 控制面/容灾流量入口/边缘计算 |
| 张家口 | edge | 阿里云 ECS | 2C2G, 1G 云硬盘 | 常态流量入口/边缘计算 |
| 北京 | batata | Intel NUC11i5 | 4C16G, 1T NVMe SSD | 计算/轻量存储 |
| 天津 | potato | Intel J3455 | 4C8G, 128G SSD + 12T HDD | 计算/重量存储 |

### 网络架构

```mermaid
graph TB
    %% 定义节点
    fork[青岛 fork<br/>控制面/容灾]
    edge[张家口 edge<br/>常态入口]
    batata[北京 batata<br/>计算节点]
    potato[天津 potato<br/>存储节点]
    
    %% 定义外部设备
    ext[外部设备]
    
    %% 定义存储服务
    pg[(PostgreSQL)]
    minio[(MinIO)]
    etcd[(Etcd)]
    juicefs[(JuiceFS)]
    
    %% 定义计算服务
    k3s[k3s集群]
    podman[Podman容器]
    binary[二进制服务]
    
    %% 连接关系
    %% 公网接入
    fork <--> edge
    
    %% NBBONE骨干网
    fork <--> batata
    fork <--> potato
    edge <--> batata
    edge <--> potato
    batata <--> potato
    
    %% Headscale授信网络
    ext --> fork
    ext --> edge
    ext --> batata
    ext --> potato
    
    %% 存储层
    potato --> pg
    potato --> minio
    potato --> etcd
    potato --> juicefs
    batata --> pg
    batata --> minio
    batata --> etcd
    batata --> juicefs
    
    %% 计算层
    potato --> k3s
    batata --> k3s
    fork --> podman
    edge --> binary
    
    class fork,edge cloud
    class batata,potato home
    class pg,minio,etcd,juicefs storage
    class k3s,podman,binary compute
```

## 核心功能

### 1. 统一网络架构

- **NBBONE 骨干网**
  - 基于 Wireguard 和自研 `nbbone-agent`
  - DNS-SD / DDNS 实现节点发现
  - 生产流量互通
  - 就近接入策略路由

- **Headscale 授信网络**
  - 外部设备接入
  - 容灾备份
  - 就近访问

- **Traefik 网关**
  - 统一服务代理
  - 地域化部署
  - 负载均衡

### 2. 存储服务

- **核心存储**
  - PostgreSQL 数据库
  - MinIO 对象存储
  - Etcd 分布式键值存储
  - JuiceFS 分布式文件系统

- **存储策略**
  - 天津主中心
  - 北京灾备
  - 定期备份同步

### 3. 计算集群

- **Kubernetes (k3s)**
  - 天津/北京分布式部署
  - 单 Master 架构
  - 应用部署和实验

- **其他计算节点**
  - 青岛：Podman 容器
  - 张家口：二进制服务

### 4. 应用生态

- **已部署应用**
  - Gitea (代码仓库/CI/CD)
  - Vaultwarden (密码管理)
  - Memos (笔记)
  - Jellyfin (媒体服务)
  - Wakapi (工作量统计)
  - alist (文件管理)

- **计划部署应用**
  - OpenWebUI / LobeChat
  - Dify
  - 其他实验性应用

### 5. 统一管理入口 (Portal)

- **功能特性**
  - 高可用部署
  - Next.js 开发
  - 基建可视化
  - WebSSH 接入
  - OIDC 认证

### 6. 可观测性

- **监控体系**
  - 统一日志
  - 性能监控
  - 告警系统
  - 链路追踪
  - 安全审计

## 部署架构

### 基础设施即代码 (IaC)

- Ansible 用于配置管理
- Terraform 用于基础设施编排
- Makefile 用于任务自动化

### CI/CD 流水线

- Gitea Actions
- 自动化部署
- 制品管理

## 网络拓扑

```mermaid
graph TB
    %% 定义节点
    fork[青岛 fork]
    edge[张家口 edge]
    batata[北京 batata]
    potato[天津 potato]
    
    %% 定义存储
    hdd[HDD存储<br/>12TB]
    ssd[SSD存储<br/>128GB]
    nvme[NVMe SSD<br/>1TB]
    
    %% 定义网络
    subgraph 公网接入
        fork <--> edge
    end
    
    subgraph 家庭网络
        batata <--> potato
    end
    
    subgraph 存储网络
        potato --> hdd
        potato --> ssd
        batata --> nvme
    end
    
    subgraph 计算网络
        batata --> k3s[k3s集群]
        potato --> k3s
    end
    
    %% 样式
    classDef cloud fill:#f9f,stroke:#333,stroke-width:2px
    classDef home fill:#bbf,stroke:#333,stroke-width:2px
    classDef storage fill:#bfb,stroke:#333,stroke-width:2px
    classDef compute fill:#fbb,stroke:#333,stroke-width:2px
    
    class fork,edge cloud
    class batata,potato home
    class hdd,ssd,nvme storage
    class k3s compute
```

## 部署指南

1. 克隆仓库
2. 配置环境变量
3. 运行部署脚本

```bash
git clone https://github.com/yourusername/ops-stack-new.git
cd ops-stack-new
make init
make deploy
```
