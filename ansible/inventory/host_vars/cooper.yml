# Gar<PERSON><PERSON> Cooper 软路由容器配置 (sh/天津)

# 基础连接信息
ansible_host: **************
ansible_user: root
ansible_ssh_common_args: '-o StrictHostKeyChecking=no'

# 地理位置信息
location: sh
region: north
datacenter: home

# 网络配置
local_subnet: "************/24"
gateway_ip: "************"
router_role: true

# Tailscale 路由配置
tailscale_advertise_routes:
  - "************/24"

# 系统配置
timezone: "Asia/Shanghai"
locale: "zh_CN.UTF-8"

# 容器特定配置
container_type: lxc
proxmox_host: potato
proxmox_node: potato

# 路由功能配置
enable_ip_forward: true
enable_nat: true
enable_dhcp: false  # 由现有 DHCP 服务器处理

# 防火墙配置
firewall_enabled: true
firewall_default_policy: ACCEPT

# 监控配置
monitoring_enabled: true
log_level: info
