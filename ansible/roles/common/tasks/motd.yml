---
- name: Create dynamic MOTD directory
  tags: common, motd
  file:
    path: /etc/update-motd.d
    state: directory
    mode: '0755'
  when: ansible_os_family == "Debian"

- name: Remove default MOTD files
  tags: common, motd
  file:
    path: "{{ item }}"
    state: absent
  with_items:
    - /etc/motd
    - /etc/update-motd.d/10-help-text
    - /etc/update-motd.d/50-motd-news
    - /etc/update-motd.d/80-livepatch
    - /etc/update-motd.d/91-release-upgrade
  when: ansible_os_family == "Debian"

- name: Create custom MOTD script
  tags: common, motd
  copy:
    src: 10-custom-motd
    dest: /etc/update-motd.d/10-custom-motd
    mode: '0755'
  when: ansible_os_family == "Debian"

- name: Ensure bc is installed (for floating point comparison)
  tags: common, motd
  apt:
    name: bc
    state: present
  when: ansible_os_family == "Debian"

- name: Configure PAM to show MOTD
  tags: common, motd
  lineinfile:
    path: /etc/pam.d/sshd
    line: "session    optional     pam_motd.so  motd=/run/motd.dynamic"
    state: present
  when: ansible_os_family == "Debian"
