services:
  headscale:
    image: headscale/headscale:{{ headscale_version | default('0.26.1') }}
    container_name: headscale
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "9090:9090"
      - "50443:50443"
    volumes:
      - /etc/headscale/config.yaml:/etc/headscale/config.yaml:ro
      - /var/lib/headscale:/var/lib/headscale
      - /var/run/headscale:/var/run/headscale
    command: serve
    networks:
      - ingress

networks:
  ingress:
    name: ingress
    external: true
