---
- name: Create headscale directories
  tags: headscale
  file:
    path: "{{ item }}"
    state: directory
    mode: '0755'
  with_items:
    - /etc/headscale
    - /var/lib/headscale

- name: Create headscale configuration
  tags: headscale
  template:
    src: config.yaml.j2
    dest: /etc/headscale/config.yaml
    mode: '0644'
  notify: restart headscale

- name: Create headscale compose file
  tags: headscale
  template:
    src: compose.yml.j2
    dest: /etc/headscale/compose.yml
    mode: '0644'

- name: Create Traefik dynamic configuration for headscale
  tags: headscale
  template:
    src: traefik-dynamic.yml.j2
    dest: /etc/traefik/dynamic/headscale.yml
    mode: '0644'

- name: Start headscale
  tags: headscale
  shell: nerdctl compose -f /etc/headscale/compose.yml up -d
