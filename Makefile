# 基础目录
ANSIBLE_DIR := ansible
TERRAFORM_DIR := terraform
GARGANTUA_DIR := $(TERRAFORM_DIR)/gargantua

# 初始化
.PHONY: init
init:
	@echo "初始化项目..."
	ansible-galaxy install -r $(ANSIBLE_DIR)/requirements.yml
	cd $(TERRAFORM_DIR) && terraform init

# 青岛控制面部署
.PHONY: deploy-qddt
deploy-qddt:
	@echo "部署青岛控制面..."
	cd $(ANSIBLE_DIR) && ansible-playbook -i inventory/hosts.yml playbooks/qddt.yml$(if $(TAGS), --tags $(TAGS),)

# Tailscale 网络部署
.PHONY: deploy-tailscale
deploy-tailscale:
	@echo "部署 Tailscale 网络..."
	cd $(ANSIBLE_DIR) && ansible-playbook -i inventory/hosts.yml playbooks/tailscale.yml$(if $(TAGS), --tags $(TAGS),)

# ==================== Gargantua 软路由 ====================

.PHONY: gargantua-init gargantua-plan gargantua-apply deploy-gargantua gargantua-destroy gargantua-full-deploy

gargantua-init:
	@echo "初始化 Gargantua..."
	cd $(GARGANTUA_DIR) && terraform init

gargantua-plan:
	@echo "规划 Gargantua 部署..."
	cd $(GARGANTUA_DIR) && terraform plan

gargantua-apply:
	@echo "部署 Gargantua 容器..."
	cd $(GARGANTUA_DIR) && terraform apply -auto-approve

deploy-gargantua:
	@echo "配置 Gargantua 软路由..."
	cd $(ANSIBLE_DIR) && ansible-playbook -i inventory/hosts.yml playbooks/gargantua.yml$(if $(TAGS), --tags $(TAGS),)

gargantua-full-deploy: gargantua-init gargantua-apply deploy-gargantua

gargantua-destroy:
	@echo "销毁 Gargantua..."
	cd $(GARGANTUA_DIR) && terraform destroy -auto-approve

# ==================== 完整部署 ====================

.PHONY: deploy
deploy: init deploy-qddt
