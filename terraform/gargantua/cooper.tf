# Cooper - sh 软路由容器
resource "proxmox_lxc" "cooper" {
  vmid             = 111
  provider         = proxmox.potato
  target_node      = "potato"
  hostname         = "cooper"
  ostemplate       = var.lxc_template
  password         = var.lxc_password
  unprivileged     = true
  onboot           = true
  start            = true
  
  # 资源配置
  memory           = 512
  swap             = 512
  cores            = 1
  
  # 根文件系统
  rootfs {
    storage = "local-lvm"
    size    = "8G"
  }
  
  # 网络配置
  network {
    name   = "eth0"
    bridge = "vmbr0"
    ip     = "**************/24"
    gw     = "************"
  }
  
  # SSH 密钥
  ssh_public_keys = var.ssh_public_keys
  
  # 启用路由功能
  features {
    nesting = true
  }
  
  tags = "gargantua;router"
}
