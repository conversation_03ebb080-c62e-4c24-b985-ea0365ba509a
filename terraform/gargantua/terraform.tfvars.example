# Batata (unsh) 配置
batata_api_url  = "https://192.168.11.10:8006/api2/json"
batata_user     = "root@pam"
batata_password = "your_batata_password"

# Potato (sh) 配置
potato_api_url  = "https://192.168.10.10:8006/api2/json"
potato_user     = "root@pam"
potato_password = "your_potato_password"

# LXC 配置
lxc_template = "local:vztmpl/debian-12-standard_12.7-1_amd64.tar.zst"
lxc_password = "your_lxc_password"

# SSH 公钥
ssh_public_keys = <<EOF
ssh-rsa AAAAB3NzaC1yc2E... your_ssh_public_key
EOF
