---
- name: Ensure fail2ban is installed
  tags: fail2ban
  apt:
    name: fail2ban
    state: present
  when: ansible_os_family == "Debian"

- name: Create fail2ban jail.local configuration
  tags: fail2ban
  copy:
    dest: /etc/fail2ban/jail.local
    content: |
      [DEFAULT]
      # Ban time in seconds (2 hours)
      bantime = 7200

      # Find time window in seconds (10 minutes)
      findtime = 600

      # Number of failures before ban
      maxretry = 3

      # Ignore local IPs
      ignoreip = 127.0.0.1/8 ::1 10.0.0.0/8 **********/12 ***********/16

      # Backend for log monitoring
      backend = auto

      [sshd]
      enabled = true
      port = ssh
      filter = sshd
      logpath = /var/log/auth.log
    mode: '0644'
    backup: yes
  notify: restart fail2ban

- name: Start and enable fail2ban service
  tags: fail2ban
  service:
    name: fail2ban
    state: started
    enabled: yes

- name: Check fail2ban status
  tags: fail2ban
  command: fail2ban-client status
  register: fail2ban_status
  changed_when: false
  failed_when: false

- name: Display fail2ban status
  tags: fail2ban
  debug:
    var: fail2ban_status.stdout_lines
  when: fail2ban_status.rc == 0
