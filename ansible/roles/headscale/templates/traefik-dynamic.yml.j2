http:
  routers:
    headscale:
      rule: Host(`{{ headscale_domain | default('headscale.' + ansible_fqdn) }}`)
      service: headscale
      tls: {}
    headscale-grpc:
      rule: Host(`{{ headscale_domain | default('headscale.' + ansible_fqdn) }}`) && PathPrefix(`/headscale`)
      service: headscale-grpc
      tls: {}
  services:
    headscale:
      loadBalancer:
        servers:
          - url: http://headscale:8080
    headscale-grpc:
      loadBalancer:
        servers:
          - url: h2c://headscale:50443
