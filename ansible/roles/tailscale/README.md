# Tailscale Role

这个 Ansible role 用于在服务器上安装和配置 Tailscale，并将其连接到 Headscale 网络。

## 功能

- 使用中国科学技术大学镜像源安装 Tailscale（适用于中国服务器）
- 自动检测节点是否已连接到 Tailscale 网络
- 从 Headscale 容器获取 preauth key
- 自动连接到 Headscale 网络
- 启用并启动 Tailscale 服务

## 要求

- 目标主机运行 Ubuntu/Debian 系统
- 已部署 Headscale 服务器
- Headscale 容器正在运行并可访问

## 变量

### 必需变量

- `headscale_url`: Headscale 服务器的 URL（在 group_vars/all.yml 中配置）

### 可选变量

- `headscale_domain`: Headscale 服务器域名（默认从 traefik_domain 生成）

## 使用方法

### 1. 在 playbook 中使用

```yaml
---
- name: Install and configure Tailscale
  hosts: all
  become: yes
  roles:
    - tailscale
```

### 2. 单独运行

```bash
ansible-playbook -i inventory/hosts.yml playbooks/tailscale.yml
```

### 3. 只在特定主机组运行

```bash
ansible-playbook -i inventory/hosts.yml playbooks/tailscale.yml --limit home
```

### 4. 使用标签

```bash
# 只运行 tailscale 相关任务
ansible-playbook -i inventory/hosts.yml playbooks/tailscale.yml --tags tailscale
```

## 注意事项

1. 该 role 使用中国科学技术大学的 Tailscale 镜像源，适用于中国境内的服务器
2. 需要确保 Headscale 服务器已正确配置并运行
3. Preauth key 的有效期为 1 小时，如果安装过程超时可能需要重新运行
4. 该 role 会自动跳过已连接的节点，可以安全地重复运行

## 故障排除

如果遇到问题，可以：

1. 检查 Headscale 服务器是否正常运行
2. 验证 headscale_url 配置是否正确
3. 查看 Ansible 输出中的调试信息
4. 手动检查 Tailscale 状态：`tailscale status`
