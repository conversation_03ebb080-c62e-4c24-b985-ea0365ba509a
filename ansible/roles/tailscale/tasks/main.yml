---
- name: Create keyrings directory
  tags: tailscale
  file:
    path: /usr/share/keyrings
    state: directory
    mode: '0755'
  when: ansible_os_family == "Debian"

- name: Download USTC Tailscale repository key
  tags: tailscale
  get_url:
    url: https://pkgs.tailscale.com/stable/debian/{{ ansible_distribution_release }}.noarmor.gpg
    dest: /usr/share/keyrings/tailscale-archive-keyring.gpg
    mode: '0644'
  when: ansible_os_family == "Debian"

- name: Add USTC Tailscale repository
  tags: tailscale
  apt_repository:
    repo: "deb [signed-by=/usr/share/keyrings/tailscale-archive-keyring.gpg] https://mirrors.ustc.edu.cn/tailscale/debian {{ ansible_distribution_release }} main"
    state: present
    filename: tailscale
  when: ansible_os_family == "Debian"

- name: Update apt cache for Tailscale
  tags: tailscale
  apt:
    update_cache: yes
  when: ansible_os_family == "Debian"

- name: Install Tailscale
  tags: tailscale
  apt:
    name: tailscale
    state: present
  when: ansible_os_family == "Debian"

- name: Make sure tailscaled is started
  tags: tailscale
  systemd:
    name: tailscaled
    state: started

- name: Check if Tailscale is already connected
  tags: tailscale
  command: tailscale status --json
  register: tailscale_status
  failed_when: false
  changed_when: false

- name: Parse Tailscale status
  tags: tailscale
  set_fact:
    tailscale_connected: "{{ (tailscale_status.stdout | from_json).BackendState == 'Running' }}"
  when: tailscale_status.rc == 0

- name: Set tailscale_connected to false if status check failed
  tags: tailscale
  set_fact:
    tailscale_connected: false
  when: tailscale_status.rc != 0

- name: Get preauth key from headscale container
  tags: tailscale
  shell: |
    nerdctl exec headscale headscale preauthkeys create --user 1 --expiration 1h --reusable
  register: preauth_key_result
  delegate_to: "{{ groups['cloud'][0] }}"
  run_once: true
  when: not tailscale_connected
  failed_when: preauth_key_result.rc != 0

- name: Debug preauth key
  tags: tailscale
  debug:
    msg: "Preauth key: {{ preauth_key_result.stdout.strip() }}"
  when: not tailscale_connected and preauth_key_result is defined

- name: Enable and start Tailscale service
  tags: tailscale
  systemd:
    name: tailscaled
    enabled: yes
    state: started

- name: Connect to Tailscale network using headscale
  tags: tailscale
  command: >
    tailscale up
    --login-server={{ headscale_url }}
    --authkey={{ preauth_key_result.stdout.strip() }}
    --accept-routes
    --accept-dns=false
    {% if tailscale_advertise_routes is defined and tailscale_advertise_routes | length > 0 %}
    --advertise-routes={{ tailscale_advertise_routes | join(',') }}
    {% endif %}
  when: not tailscale_connected and preauth_key_result is defined
  register: tailscale_up_result

- name: Display Tailscale connection result
  tags: tailscale
  debug:
    msg: "Tailscale connection result: {{ tailscale_up_result }}"
  when: not tailscale_connected and preauth_key_result is defined

# 路由器模式配置
- name: Enable IP forwarding for router mode
  tags: tailscale
  sysctl:
    name: net.ipv4.ip_forward
    value: '1'
    state: present
    reload: yes
  when: router_role is defined and router_role

- name: Enable IPv6 forwarding for router mode
  tags: tailscale
  sysctl:
    name: net.ipv6.conf.all.forwarding
    value: '1'
    state: present
    reload: yes
  when: router_role is defined and router_role

- name: Configure NAT rules for local subnet
  tags: tailscale
  iptables:
    table: nat
    chain: POSTROUTING
    source: "{{ local_subnet }}"
    out_interface: tailscale0
    jump: MASQUERADE
    comment: "Tailscale NAT for {{ local_subnet }}"
  when:
    - router_role is defined and router_role
    - local_subnet is defined
    - enable_nat is defined and enable_nat

- name: Save iptables rules
  tags: tailscale
  shell: iptables-save > /etc/iptables/rules.v4
  when:
    - router_role is defined and router_role
    - ansible_os_family == "Debian"
