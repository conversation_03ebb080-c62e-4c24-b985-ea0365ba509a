services:
  traefik:
    image: traefik:v3.4.1
    container_name: traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - /opt/certs:/opt/certs
      - /etc/traefik/traefik.yml:/etc/traefik/traefik.yml:ro
      - /etc/traefik/dynamic:/etc/traefik/dynamic:ro
      - /var/lib/traefik:/var/lib/traefik
    networks:
      - ingress
  whoami:
    image: traefik/whoami:v1.11.0
    restart: unless-stopped
    networks:
      - ingress
  frps:
    image: snowdreamtech/frps:alpine
    restart: unless-stopped
    ports:
      - "7000:7000"
    networks:
      - ingress

networks:
  ingress:
    name: ingress
    driver: bridge 